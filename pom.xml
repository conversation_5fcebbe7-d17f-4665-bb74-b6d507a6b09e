<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.ly.travel.shared.mobility</groupId>
    <artifactId>shared-mobility-asr-core-parent</artifactId>
    <version>*******</version>

    <name>LY shared-mobility-asr-core parent</name>
    <url>http://www.ly.com</url>
    <description>LY shared-mobility-asr-core Application Parent</description>
    <packaging>pom</packaging>

    <properties>
        <sof.version>********</sof.version>
        <spring.version>4.3.25.RELEASE</spring.version>

        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <maven.compiler.encoding>UTF-8</maven.compiler.encoding>

        <jacoco.skip>true</jacoco.skip>
        <jacoco.path>${project.build.directory}/jacoco-ut.exec</jacoco.path>

        <sonar.core.codeCoveragePlugin>jacoco</sonar.core.codeCoveragePlugin>
        <sonar.dynamicAnalysis>reuseReports</sonar.dynamicAnalysis>
        <sonar.jacoco.itReportPath>target/jacoco-it.exec</sonar.jacoco.itReportPath>
        <sonar.jacoco.reportPath>target/jacoco-ut.exec</sonar.jacoco.reportPath>

        <cglib.version>2.2</cglib.version>
        <asm.version>3.1</asm.version>
        <aspectj.version>1.8.14</aspectj.version>
        <velocity.version>1.7</velocity.version>
        <velocity.tools.version>2.0</velocity.tools.version>
        <dbcp.version>1.4</dbcp.version>
        <ibatis.version>2.3.4.726</ibatis.version>
        <ibatis-spring.version>1.1.0</ibatis-spring.version>
        <mysql.version>5.1.38</mysql.version>
        <mariadb.version>1.3.7</mariadb.version>
        <jaxb.version>2.3.0</jaxb.version>
        <slf4j.version>1.7.30</slf4j.version>
        <log4j.version>1-LY-EMPTY</log4j.version>
        <log4j2.version>2.17.2</log4j2.version>
        <!-- jupiter5+mockito-->
        <junit.jupiter.version>5.10.0</junit.jupiter.version>
        <mockito.core.version>4.11.0</mockito.core.version>
        <mockito.junit.jupiter.version>4.11.0</mockito.junit.jupiter.version>
        <spring_test_plus.version>0.0.5</spring_test_plus.version>
        <dal-new.version>3.6.6</dal-new.version>
        <lombok.version>1.18.32</lombok.version>
        <mapstruct.version>1.4.1.4.LY.Final</mapstruct.version>

        <!-- tcbase cache -->
        <tcbase-cache.version>3.6.8</tcbase-cache.version>
        <!-- toolkit -->
        <flight.toolkit.version>1.0.5.2.RELEASE</flight.toolkit.version>
        <!-- excel ���� ����-->
        <easyexcel.version>3.2.1</easyexcel.version>
        <!-- log4j2 disruptor -->
        <disruptor.version>1.2.21</disruptor.version>

        <mybatis.plus.version>3.5.1</mybatis.plus.version>
        <car-common.version>1.0.2.21.RELEASE</car-common.version>
        <hutool.version>5.8.12</hutool.version>
        <car-common.version>1.0.2.21.RELEASE</car-common.version>
        <!-- 订单服务 -->
        <orderservice.version>1.0.4.2.RELEASE</orderservice.version>
        <!-- 订单查询服务 -->
        <ordercore.version>1.0.3.4-SNAPSHOT</ordercore.version>
        <!-- 订单交易服务 -->
        <tradecore.version>1.0.7.0.RELEASE</tradecore.version>


    </properties>

    <modules>
        <module>app/web</module>
        <module>app/test</module>
        <module>app/integration</module>
        <module>app/biz</module>
        <module>app/dal</module>
        <module>app/vue</module>
        <module>assembly</module>
        <module>webdocs</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <!-- project dependency -->
            <dependency>
                <groupId>com.ly.travel.shared.mobility</groupId>
                <artifactId>shared-mobility-asr-core-web</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ly.travel.shared.mobility</groupId>
                <artifactId>shared-mobility-asr-core-biz</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ly.travel.shared.mobility</groupId>
                <artifactId>shared-mobility-asr-core-dal</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ly.travel.shared.mobility</groupId>
                <artifactId>shared-mobility-asr-core-integration</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ly.travel.shared.mobility</groupId>
                <artifactId>shared-mobility-asr-core-test</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ly.travel.shared.mobility</groupId>
                <artifactId>shared-mobility-asr-core-assembly</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- SOF bom import -->
            <dependency>
                <groupId>com.ly.sof</groupId>
                <artifactId>sof-bom</artifactId>
                <version>${sof.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- BEGIN: Spring -->
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-beans</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-context</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-core</artifactId>
                <version>${spring.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>commons-logging</groupId>
                        <artifactId>commons-logging</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-tx</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-web</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-webmvc</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-aop</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-context-support</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-jdbc</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-orm</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-aspects</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <!-- END: Spring -->

            <!-- Spring dependecy -->
            <dependency>
                <groupId>org.aspectj</groupId>
                <artifactId>aspectjweaver</artifactId>
                <version>${aspectj.version}</version>
                <scope>runtime</scope>
            </dependency>
            <dependency>
                <groupId>org.aspectj</groupId>
                <artifactId>aspectjrt</artifactId>
                <version>${aspectj.version}</version>
            </dependency>
            <dependency>
                <groupId>cglib</groupId>
                <artifactId>cglib</artifactId>
                <version>${cglib.version}</version>
            </dependency>
            <dependency>
                <groupId>cglib</groupId>
                <artifactId>cglib-nodep</artifactId>
                <version>${cglib.version}</version>
            </dependency>
            <dependency>
                <groupId>asm</groupId>
                <artifactId>asm</artifactId>
                <version>${asm.version}</version>
            </dependency>

            <!-- ibatis -->
            <dependency>
                <groupId>com.ibatis</groupId>
                <artifactId>ibatis</artifactId>
                <version>${ibatis.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis-2-spring</artifactId>
                <version>${ibatis-spring.version}</version>
            </dependency>

            <!-- tools -->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.velocity</groupId>
                <artifactId>velocity</artifactId>
                <version>${velocity.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.velocity</groupId>
                <artifactId>velocity-tools</artifactId>
                <version>${velocity.tools.version}</version>
            </dependency>
            <dependency>
                <groupId>org.hibernate</groupId>
                <artifactId>hibernate-validator</artifactId>
                <version>6.1.0.Final</version>
            </dependency>
            <dependency>
                <groupId>javax.servlet</groupId>
                <artifactId>servlet-api</artifactId>
                <version>2.5</version>
            </dependency>
            <dependency>
                <groupId>javax.servlet</groupId>
                <artifactId>jstl</artifactId>
                <version>1.2</version>
            </dependency>
            <dependency>
                <groupId>commons-logging</groupId>
                <artifactId>commons-logging</artifactId>
                <version>1.1.1</version>
            </dependency>

            <dependency>
                <groupId>commons-dbcp</groupId>
                <artifactId>commons-dbcp</artifactId>
                <version>${dbcp.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ly.flight.toolkit</groupId>
                <artifactId>object-diff</artifactId>
                <version>${flight.toolkit.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ly.flight.toolkit</groupId>
                <artifactId>deploy-static-resource</artifactId>
                <version>${flight.toolkit.version}</version>
            </dependency>
            <dependency>
                <artifactId>sof-batis-gen-dependency</artifactId>
                <groupId>com.ly.flight.toolkit</groupId>
                <version>${flight.toolkit.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ly.flight.toolkit</groupId>
                <artifactId>sof-dev-launcher</artifactId>
                <version>${flight.toolkit.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ly.flight.toolkit</groupId>
                <artifactId>spring-test-migration</artifactId>
                <version>${flight.toolkit.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ly.flight.intl</groupId>
                <artifactId>spring-test-plus</artifactId>
                <version>${spring_test_plus.version}</version>
                <scope>test</scope>
            </dependency>

            <!--jaxb-->
            <dependency>
                <groupId>javax.xml.bind</groupId>
                <artifactId>jaxb-api</artifactId>
                <version>${jaxb.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sun.xml.bind</groupId>
                <artifactId>jaxb-core</artifactId>
                <version>${jaxb.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sun.xml.bind</groupId>
                <artifactId>jaxb-impl</artifactId>
                <version>${jaxb.version}</version>
            </dependency>
            <dependency>
                <groupId>javax.activation</groupId>
                <artifactId>activation</artifactId>
                <version>1.1.1</version>
            </dependency>

            <!-- JDBC Driver -->
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mariadb.jdbc</groupId>
                <artifactId>mariadb-java-client</artifactId>
                <version>${mariadb.version}</version>
            </dependency>

            <!-- log dependecies -->
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-api</artifactId>
                <version>${slf4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-log4j12</artifactId>
                <version>${log4j.version}</version>
            </dependency>
            <dependency>
                <groupId>log4j</groupId>
                <artifactId>log4j</artifactId>
                <version>${log4j.version}</version>
            </dependency>

            <!-- log4j2 -->
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-api</artifactId>
                <version>${log4j2.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-core</artifactId>
                <version>${log4j2.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-slf4j-impl</artifactId>
                <version>${log4j2.version}</version>
            </dependency>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>jcl-over-slf4j</artifactId>
                <version>${slf4j.version}</version>
                <scope>runtime</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-web</artifactId>
                <version>${log4j2.version}</version>
                <scope>runtime</scope>
            </dependency>
            <dependency>
                <groupId>com.conversantmedia</groupId>
                <artifactId>disruptor</artifactId>
                <version>${disruptor.version}</version>
            </dependency>

            <!-- Test dependecies -->
            <!-- junit-jupiter-->
            <dependency>
                <groupId>org.junit.jupiter</groupId>
                <artifactId>junit-jupiter</artifactId>
                <version>${junit.jupiter.version}</version>
                <scope>test</scope>
            </dependency>
            <!-- Mockito���� -->
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-core</artifactId>
                <version>${mockito.core.version}</version>
                <scope>test</scope>
            </dependency>
            <!-- JUnit5��Mockito�������� -->
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-junit-jupiter</artifactId>
                <version>${mockito.junit.jupiter.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-test</artifactId>
                <version>${spring.version}</version>
            </dependency>

            <!-- tcdbms -->
            <dependency>
                <groupId>com.ly.dal</groupId>
                <artifactId>dal-new</artifactId>
                <version>${dal-new.version}</version>
            </dependency>

            <!-- TODO ��Ҫʱ���� -->
            <!--            <dependency>-->
            <!--                <groupId>com.ly.tcbase</groupId>-->
            <!--                <artifactId>cache</artifactId>-->
            <!--                <version>${tcbase-cache.version}</version>-->
            <!--            </dependency>-->

            <!-- excel -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${easyexcel.version}</version>
            </dependency>

            <!-- mybatis -->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus</artifactId>
                <version>${mybatis.plus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-generator</artifactId>
                <version>${mybatis.plus.version}</version>
            </dependency>
            <dependency>
                <groupId>org.freemarker</groupId>
                <artifactId>freemarker</artifactId>
                <version>2.3.31</version>
            </dependency>

            <dependency>
                <groupId>com.ly.travel.car</groupId>
                <artifactId>shared-mobility-car-common-rpc-turbomq</artifactId>
                <version>${car-common.version}</version>
            </dependency>

            <!-- hutool -->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>

            <dependency>
                <groupId>redis.clients</groupId>
                <artifactId>jedis</artifactId>
                <version>4.4.3</version>
            </dependency>

            <dependency>
                <groupId>com.github.junrar</groupId>
                <artifactId>junrar</artifactId>
                <version>7.5.4</version>
            </dependency>

            <!-- 7-Zip-JBinding for RAR v5 support -->
            <dependency>
                <groupId>net.sf.sevenzipjbinding</groupId>
                <artifactId>sevenzipjbinding</artifactId>
                <version>16.02-2.01</version>
            </dependency>
            <dependency>
                <groupId>net.sf.sevenzipjbinding</groupId>
                <artifactId>sevenzipjbinding-all-platforms</artifactId>
                <version>16.02-2.01</version>
            </dependency>

            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-compress</artifactId>
                <version>1.21</version>
                <!-- 或者使用最新版本 -->
            </dependency>

            <!--用车订单列表查询-->
            <dependency>
                <groupId>com.ly.travel.car</groupId>
                <artifactId>shared-mobility-order-service-facade</artifactId>
                <version>${orderservice.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ly.travel.car</groupId>
                <artifactId>shared-mobility-order-service-model</artifactId>
                <version>${orderservice.version}</version>
            </dependency>

            <dependency>
                <groupId>com.ly.travel.car</groupId>
                <artifactId>shared-mobility-order-core-facade</artifactId>
                <version>${ordercore.version}</version>
            </dependency>

            <dependency>
                <groupId>com.ly.travel.car</groupId>
                <artifactId>shared-mobility-trade-core-model</artifactId>
                <version>${tradecore.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${lombok.version}</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>build-helper-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>versions-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-scm-plugin</artifactId>
                <configuration>
                    <connectionType>connection</connectionType>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-war-plugin</artifactId>
            </plugin>
        </plugins>

        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-jar-plugin</artifactId>
                    <version>2.4</version>
                    <!-- <configuration> <archive> <manifestFile>src/main/resources/META-INF/MANIFEST.MF</manifestFile> </archive> </configuration> -->
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.8.1</version>
                    <configuration>
                        <source>${jdk.version}</source>
                        <target>${jdk.version}</target>
                        <encoding>UTF-8</encoding>
                        <showWarnings>true</showWarnings>
                        <fork>true</fork>
                        <compilerVersion>${jdk.version}</compilerVersion>
                        <annotationProcessorPaths>
                            <path>
                                <groupId>org.mapstruct</groupId>
                                <artifactId>mapstruct-processor</artifactId>
                                <version>${mapstruct.version}</version>
                            </path>
                            <path>
                                <groupId>org.projectlombok</groupId>
                                <artifactId>lombok</artifactId>
                                <version>${lombok.version}</version>
                            </path>
                            <path>
                                <groupId>org.projectlombok</groupId>
                                <artifactId>lombok-mapstruct-binding</artifactId>
                                <version>0.2.0</version>
                            </path>
                        </annotationProcessorPaths>
                        <compilerArgs>
                            <compilerArg>
                                -Amapstruct.defaultComponentModel=spring
                            </compilerArg>
                            <compilerArg>
                                -Amapstruct.unmappedTargetPolicy=IGNORE
                            </compilerArg>
                            <compilerArg>
                                -Amapstruct.suppressGeneratorTimestamp=true
                            </compilerArg>
                        </compilerArgs>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-assembly-plugin</artifactId>
                    <version>2.5.4</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-resources-plugin</artifactId>
                    <version>2.6</version>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>build-helper-maven-plugin</artifactId>
                    <version>1.9.1</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-javadoc-plugin</artifactId>
                    <version>2.7</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-release-plugin</artifactId>
                    <version>2.4.2</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-scm-plugin</artifactId>
                    <version>1.8.1</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-source-plugin</artifactId>
                    <version>2.2</version>
                    <executions>
                        <execution>
                            <id>attach-sources</id>
                            <goals>
                                <goal>jar</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>3.2.5</version>
                    <configuration>
                        <argLine>
                            ${surefireArgLine}
                        </argLine>
                        <testFailureIgnore>true</testFailureIgnore>
                        <includes>
                            <include>**/*Test.java</include>
                        </includes>
                        <excludes>
                            <exclude>**/test/**</exclude>
                        </excludes>
                        <systemPropertyVariables>
                            <jacoco-agent.destfile>${project.build.directory}/jacoco-ut.exec</jacoco-agent.destfile>
                        </systemPropertyVariables>
                    </configuration>
                    <dependencies>
                        <dependency>
                            <groupId>org.junit.jupiter</groupId>
                            <artifactId>junit-jupiter-engine</artifactId>
                            <version>${junit.jupiter.version}</version>
                        </dependency>
                    </dependencies>
                </plugin>
                <plugin>
                    <groupId>org.jacoco</groupId>
                    <artifactId>jacoco-maven-plugin</artifactId>
                    <version>0.8.11</version>
                    <configuration>
                        <skip>${jacoco.skip}</skip>
                        <destFile>${jacoco.path}</destFile>
                        <dataFile>${jacoco.path}</dataFile>
                        <sessionId>jacoco_coverage</sessionId>
                        <outputDirectory>${project.reporting.outputDirectory}/jacoco</outputDirectory>
                        <dumpOnExit>true</dumpOnExit>
                    </configuration>
                    <executions>
                        <execution>
                            <id>pre-test</id>
                            <phase>process-classes</phase>
                            <goals>
                                <goal>prepare-agent</goal>
                            </goals>
                            <configuration>
                                <propertyName>coverageAgent</propertyName>
                                <propertyName>surefireArgLine</propertyName>
                            </configuration>
                        </execution>
                        <execution>
                            <id>report</id>
                            <goals>
                                <goal>report</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>exec-maven-plugin</artifactId>
                    <version>1.2.1</version>
                </plugin>

                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>versions-maven-plugin</artifactId>
                    <version>2.0</version>
                    <dependencies>
                        <dependency>
                            <groupId>org.apache.maven.doxia</groupId>
                            <artifactId>doxia-core</artifactId>
                            <version>1.2</version>
                        </dependency>
                        <dependency>
                            <groupId>org.apache.maven.doxia</groupId>
                            <artifactId>doxia-site-renderer</artifactId>
                            <version>1.2</version>
                        </dependency>
                    </dependencies>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-war-plugin</artifactId>
                    <version>2.4</version>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

    <reporting>
        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>findbugs-maven-plugin</artifactId>
                <version>3.0.5</version>
                <configuration>
                    <xmlOutput>true</xmlOutput>
                    <threshold>High</threshold>
                    <effort>Default</effort>
                    <relaxed>true</relaxed>
                    <findbugsXmlOutput>true</findbugsXmlOutput>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>taglist-maven-plugin</artifactId>
                <version>2.4</version>
                <configuration>
                    <tags>
                        <tag>TODO</tag>
                        <tag>@todo</tag>
                        <tag>FIXME</tag>
                        <tag>@fixme</tag>
                        <tag>@deprecated</tag>
                    </tags>
                </configuration>
            </plugin>
        </plugins>
    </reporting>

    <profiles>
        <profile>
            <id>dev</id>
            <activation>
                <jdk>1.8</jdk>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <jdk.version>1.8</jdk.version>
                <maven.compiler.source>1.8</maven.compiler.source>
                <maven.compiler.target>1.8</maven.compiler.target>
                <maven.compiler.compilerVersion>1.8</maven.compiler.compilerVersion>
            </properties>
        </profile>

        <profile>
            <id>test</id>
            <activation>
                <jdk>1.8</jdk>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <jdk.version>1.8</jdk.version>
                <maven.compiler.source>1.8</maven.compiler.source>
                <maven.compiler.target>1.8</maven.compiler.target>
                <maven.compiler.compilerVersion>1.8</maven.compiler.compilerVersion>
            </properties>
        </profile>

        <profile>
            <id>jdk8</id>
            <activation>
                <jdk>1.8</jdk>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <jdk.version>1.8</jdk.version>
                <maven.compiler.source>1.8</maven.compiler.source>
                <maven.compiler.target>1.8</maven.compiler.target>
                <maven.compiler.compilerVersion>1.8</maven.compiler.compilerVersion>
            </properties>
        </profile>

        <profile>
            <id>jdk17</id>
            <activation>
                <activeByDefault>false</activeByDefault>
                <jdk>17</jdk>
            </activation>
            <properties>
                <jdk.version>17</jdk.version>
                <maven.compiler.source>17</maven.compiler.source>
                <maven.compiler.target>17</maven.compiler.target>
                <maven.compiler.compilerVersion>17</maven.compiler.compilerVersion>
            </properties>
        </profile>

        <profile>
            <id>external</id>
            <repositories>
                <repository>
                    <id>aliyun</id>
                    <name>Aliyun Repository</name>
                    <url>https://maven.aliyun.com/nexus/content/groups/public/</url>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                </repository>
            </repositories>
            <pluginRepositories>
                <pluginRepository>
                    <id>aliyun</id>
                    <name>Aliyun Repository</name>
                    <url>https://maven.aliyun.com/nexus/content/groups/public/</url>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                    <snapshots>
                        <enabled>false</enabled>
                    </snapshots>
                </pluginRepository>
            </pluginRepositories>
        </profile>
    </profiles>
    <repositories>
        <repository>
            <id>17usoft</id>
            <name>LY Share Repository</name>
            <url>http://nexus.17usoft.com/repository/mvn-all/</url>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
    </repositories>
    <pluginRepositories>
        <pluginRepository>
            <id>public</id>
            <url>http://nexus.17usoft.com/repository/mvn-all/</url>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>
    <distributionManagement>
        <snapshotRepository>
            <id>nexus-snapshots</id>
            <name>nexus-snapshots</name>
            <url>http://nexus.17usoft.com/repository/mvn-flight-snapshot/</url>
            <uniqueVersion>false</uniqueVersion>
        </snapshotRepository>
        <repository>
            <id>nexus-releases</id>
            <name>nexus-releases</name>
            <url>http://nexus.17usoft.com/repository/mvn-flight-release/</url>
        </repository>
    </distributionManagement>
</project>
