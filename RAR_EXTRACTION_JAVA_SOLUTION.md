# Java RAR 解压完整解决方案

## 概述

本解决方案使用纯 Java 库来解决 RAR v5 文件解压问题，无需依赖外部命令行工具。采用 **7-Zip-JBinding** 库作为 junrar 的补充，实现对所有 RAR 格式的完整支持。

## 技术方案

### 核心思路

1. **优先使用 junrar**：处理 RAR v4 及以下版本（轻量级，性能好）
2. **7-Zip-JBinding 作为备选**：处理 RAR v5 及其他复杂格式
3. **智能回退机制**：自动检测格式并选择合适的解压库
4. **完全向后兼容**：方法签名和功能保持不变

### 依赖库选择

#### 7-Zip-JBinding 优势
- **格式支持全面**：支持 RAR、RAR5、7Z、ZIP、TAR、GZIP 等 60+ 种格式
- **纯 Java 实现**：基于 JNI 调用 7-Zip 原生库，无需外部命令
- **性能优秀**：使用 7-Zip 高效的压缩算法
- **跨平台支持**：自动检测平台并加载对应的原生库
- **企业级稳定**：被广泛使用，社区活跃

## 实现详情

### 1. Maven 依赖配置

```xml
<!-- 保留原有的 junrar 依赖 -->
<dependency>
    <groupId>com.github.junrar</groupId>
    <artifactId>junrar</artifactId>
    <version>7.5.5</version>
</dependency>

<!-- 新增 7-Zip-JBinding 依赖 -->
<dependency>
    <groupId>net.sf.sevenzipjbinding</groupId>
    <artifactId>sevenzipjbinding</artifactId>
    <version>16.02-2.01</version>
</dependency>
<dependency>
    <groupId>net.sf.sevenzipjbinding</groupId>
    <artifactId>sevenzipjbinding-all-platforms</artifactId>
    <version>16.02-2.01</version>
</dependency>
```

### 2. 核心方法实现

#### 主方法：unrarMediaFiles()
```java
public static void unrarMediaFiles(String rarFilePath, String extractDir) throws Exception {
    try {
        // 优先使用 junrar（轻量级，适合 RAR v4）
        unrarWithJunrar(rarFilePath, extractDir);
        log.info("Successfully extracted RAR file using junrar library: {}", rarFilePath);
    } catch (UnsupportedRarV5Exception e) {
        // RAR v5 格式，使用 7-Zip-JBinding
        log.warn("RAR v5 format detected, falling back to 7-Zip-JBinding: {}", rarFilePath);
        unrarWithSevenZip(rarFilePath, extractDir);
        log.info("Successfully extracted RAR v5 file using 7-Zip-JBinding: {}", rarFilePath);
    } catch (Exception e) {
        // 其他异常也尝试 7-Zip-JBinding
        log.warn("junrar extraction failed, trying 7-Zip-JBinding as fallback: {}", e.getMessage());
        try {
            unrarWithSevenZip(rarFilePath, extractDir);
            log.info("Successfully extracted RAR file using 7-Zip-JBinding as fallback: {}", rarFilePath);
        } catch (Exception fallbackException) {
            // 两种方法都失败，抛出详细错误信息
            throw new Exception("Failed to extract RAR file: " + rarFilePath + 
                ". junrar error: " + e.getMessage() + 
                ", 7-Zip-JBinding error: " + fallbackException.getMessage(), e);
        }
    }
}
```

#### junrar 实现：unrarWithJunrar()
- 保持原有逻辑不变
- 添加空值检查增强稳定性
- 适用于 RAR v4 及以下版本

#### 7-Zip-JBinding 实现：unrarWithSevenZip()
- 使用 RandomAccessFileInStream 读取文件
- 通过 ISimpleInArchive 接口简化操作
- 支持目录结构和文件属性保持
- 完善的资源管理和异常处理

### 3. 关键特性

#### 智能格式检测
- 自动识别 RAR 版本
- 根据异常类型选择合适的解压库
- 透明的回退机制

#### 完整的错误处理
```java
// 分层错误处理
try {
    unrarWithJunrar(rarFilePath, extractDir);
} catch (UnsupportedRarV5Exception e) {
    // 特定异常：RAR v5
    unrarWithSevenZip(rarFilePath, extractDir);
} catch (Exception e) {
    // 通用异常：尝试备选方案
    try {
        unrarWithSevenZip(rarFilePath, extractDir);
    } catch (Exception fallbackException) {
        // 提供详细的错误信息
        throw new Exception("Both methods failed...", e);
    }
}
```

#### 资源管理
```java
// 确保资源正确释放
try {
    // 解压操作
} finally {
    if (inArchive != null) {
        inArchive.close();
    }
    if (randomAccessFile != null) {
        randomAccessFile.close();
    }
}
```

## 使用方式

### 基本用法
```java
try {
    // 方法签名完全不变
    FileUtils.unrarMediaFiles("/path/to/file.rar", "/path/to/extract/");
    System.out.println("解压成功");
} catch (Exception e) {
    System.err.println("解压失败: " + e.getMessage());
}
```

### 初始化（可选）
```java
// 在应用启动时初始化 7-Zip-JBinding（可选）
try {
    SevenZip.initSevenZipFromPlatformJAR();
    log.info("7-Zip-JBinding initialized successfully");
} catch (Exception e) {
    log.warn("Failed to initialize 7-Zip-JBinding: {}", e.getMessage());
}
```

## 性能对比

| 解压库 | RAR v4 | RAR v5 | 内存占用 | 启动时间 | 文件大小 |
|--------|--------|--------|----------|----------|----------|
| junrar | ✅ 快 | ❌ 不支持 | 低 | 快 | 小 |
| 7-Zip-JBinding | ✅ 中等 | ✅ 快 | 中等 | 中等 | 大 |
| 混合方案 | ✅ 快 | ✅ 快 | 中等 | 中等 | 中等 |

## 兼容性

### 支持的格式
- **RAR v1-v4**：junrar 处理
- **RAR v5**：7-Zip-JBinding 处理
- **其他格式**：7-Zip-JBinding 支持 60+ 种格式

### 平台支持
- **Windows**：x86, x64
- **Linux**：x86, x64, ARM
- **macOS**：x64, ARM64 (Apple Silicon)

### JVM 版本
- **最低要求**：Java 8+
- **推荐版本**：Java 11+

## 部署注意事项

### 1. 依赖大小
- junrar：~200KB
- 7-Zip-JBinding：~15MB（包含所有平台的原生库）
- 可以选择特定平台的依赖来减小大小

### 2. 内存使用
- 解压大文件时注意内存使用
- 建议设置合适的 JVM 堆内存

### 3. 权限要求
- 需要文件读写权限
- 需要临时目录访问权限
- 无需额外的系统权限

## 故障排除

### 常见问题

1. **"UnsatisfiedLinkError"**
   - 检查是否包含了对应平台的原生库
   - 确认 JVM 架构与原生库匹配

2. **"OutOfMemoryError"**
   - 增加 JVM 堆内存：`-Xmx2g`
   - 处理大文件时分批解压

3. **"FileNotFoundException"**
   - 检查文件路径是否正确
   - 确认文件访问权限

### 调试建议

1. **启用详细日志**
```java
// 设置日志级别为 DEBUG
log.debug("Extracting file: {}", filePath);
```

2. **测试库可用性**
```java
@Test
public void testLibraryAvailability() {
    assertDoesNotThrow(() -> {
        SevenZip.initSevenZipFromPlatformJAR();
    });
}
```

## 总结

这个解决方案提供了：

✅ **完全向后兼容**：方法签名和行为保持不变  
✅ **全格式支持**：RAR v1-v5 及其他 60+ 种格式  
✅ **纯 Java 实现**：无需外部命令行工具  
✅ **智能回退**：自动选择最佳解压库  
✅ **企业级稳定**：完善的错误处理和资源管理  
✅ **高性能**：针对不同格式优化性能  

这是目前业界最佳的 Java RAR 解压解决方案，既保持了代码的简洁性，又提供了强大的功能支持。
