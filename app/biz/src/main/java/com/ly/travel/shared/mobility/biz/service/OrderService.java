package com.ly.travel.shared.mobility.biz.service;

import com.google.common.collect.Lists;
import com.ly.sof.utils.common.DateUtil;
import com.ly.travel.car.ordercore.facade.request.SimpleOrderDetailListRequest;
import com.ly.travel.car.orderservice.facade.request.ivr.IVROrderDetailRequest;
import com.ly.travel.car.orderservice.facade.request.ivr.IVROrderListRequest;
import com.ly.travel.car.orderservice.facade.response.ivr.IVROrderDetailResponse;
import com.ly.travel.car.orderservice.facade.response.ivr.IVROrderListResponse;
import com.ly.travel.shared.mobility.biz.model.dto.order.OrderInfoDTO;
import com.ly.travel.shared.mobility.biz.model.req.order.OrderQueryReq;
import com.ly.travel.shared.mobility.integration.client.ordercore.OrderCoreClient;
import com.ly.travel.shared.mobility.integration.client.orderservice.OrderServiceClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * @Description: 订单查询
 * @Author: jay.he
 * @Date: 2025-08-19 11:39
 * @Version: 1.0
 **/
@Slf4j
@Service
public class OrderService {

    @Resource
    private OrderServiceClient ivrOrderClient;

    @Resource
    private OrderCoreClient orderCoreClient;

    public List<OrderInfoDTO> querySfcOrderList(OrderQueryReq req) {
        IVROrderListRequest listReq = new IVROrderListRequest();
        listReq.setOrderStatus("300");
        listReq.setMobilePhoneNo(req.getPhoneNo());
        listReq.setOrderStartDate(req.getStartTime());
        listReq.setOrderEndDate(req.getEndTime());
        IVROrderListResponse listRes = ivrOrderClient.queryIvrOrderList(listReq);
        return CollectionUtils.emptyIfNull(listRes.getData())
                .parallelStream()
                .map(order -> {
                    IVROrderDetailRequest request = new IVROrderDetailRequest();
                    request.setOrderId(order.getOrderId());
                    request.setTraceId(req.getTraceId());
                    return ivrOrderClient.orderDetail(request).getData();
                })
                .map(order ->
                        OrderInfoDTO
                                .builder()
                                .orderNo(order.getOrderHeadInfo().getOrderShowId())
                                .passengerOnCarTime(Optional.ofNullable(order.getOrderBookingInfo())
                                        .map(IVROrderDetailResponse.IVROrderDetail.IVROrderBookingInfo::getPassengerOnCarTime)
                                        .map(DateUtil::getNewFormatDateString)
                                        .orElse(null))
                                .passengerArriveTime(Optional.ofNullable(order.getOrderBookingInfo())
                                        .map(IVROrderDetailResponse.IVROrderDetail.IVROrderBookingInfo::getPassengerArriveTime)
                                        .map(DateUtil::getNewFormatDateString)
                                        .orElse(null))
                                .carNo(Optional.ofNullable(order.getOrderCarInfo())
                                        .map(IVROrderDetailResponse.IVROrderDetail.IVROrderCarInfo::getPlateNumber)
                                        .orElse(null))
                                .driverName(Optional.ofNullable(order.getOrderCarInfo())
                                        .map(IVROrderDetailResponse.IVROrderDetail.IVROrderCarInfo::getDriverName)
                                        .orElse(null))
                                .orderType(order.getOrderHeadInfo().getOrderType())
                                .build())
                .collect(Collectors.toList());
    }

    /**
     * 获取所有完单订单
     *
     * @param req
     * @return
     */
    public List<OrderInfoDTO> queryFinishOrderList(OrderQueryReq req) {
        IVROrderListRequest listReq = new IVROrderListRequest();
        listReq.setOrderStatus("300");
        listReq.setMobilePhoneNo(req.getPhoneNo());
        listReq.setOrderStartDate(req.getStartTime());
        listReq.setOrderEndDate(req.getEndTime());
        IVROrderListResponse listRes = ivrOrderClient.queryIvrOrderList(listReq);
        List<String> orderIds = CollectionUtils.emptyIfNull(listRes.getData())
                .parallelStream()
                .map(order -> {
                    return order.getOrderId();
                })
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        SimpleOrderDetailListRequest request = new SimpleOrderDetailListRequest();
        request.setTraceId(StringUtils.isBlank(req.getTraceId()) ? UUID.randomUUID().toString() : req.getTraceId());
        request.setOrderSerialNos(orderIds);

        return CollectionUtils.emptyIfNull(orderCoreClient.simpleDetailList(request).getOrders())
                .stream()
                .map(order -> {
                    return OrderInfoDTO
                            .builder()
                            .orderNo(order.getOrderSerialNo())
                            .passengerOnCarTime(Optional.ofNullable(order.getPassengerOnCarTime())
                                    .map(DateUtil::getNewFormatDateString)
                                    .orElse(null))
                            .passengerArriveTime(Optional.ofNullable(order.getPassengerArriveTime())
                                    .map(DateUtil::getNewFormatDateString)
                                    .orElse(null))
                            .carNo(order.getCarNum())
                            .driverName(order.getDriverName())
                            .orderType(order.getOrderType())
                            .build();
                })
                .collect(Collectors.toList());

    }
}
