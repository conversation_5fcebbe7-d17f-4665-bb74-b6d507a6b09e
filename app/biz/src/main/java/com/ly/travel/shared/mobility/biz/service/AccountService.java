package com.ly.travel.shared.mobility.biz.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ly.travel.shared.mobility.dal.mybatisplus.travelcarasr.dao.AsrAccountDao;
import com.ly.travel.shared.mobility.dal.mybatisplus.travelcarasr.model.AsrAccountDO;
import com.ly.travel.shared.mobility.integration.enums.AccountStatusEnum;
import com.ly.travel.shared.mobility.integration.exception.AsrCoreWarnException;
import com.ly.travel.shared.mobility.integration.utils.dto.InspectorAccountDTO;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> By Houce
 * @since 2025/8/25
 */
@Service
public class AccountService {

    @Resource
    private AsrAccountDao accountDao;
    @Value("${sof-env}")
    private String env;


    public AsrAccountDO getAccount(String mobile){
        LambdaQueryWrapper<AsrAccountDO> wrapper = Wrappers.<AsrAccountDO>lambdaQuery().eq(AsrAccountDO::getMobile, mobile).eq(AsrAccountDO::getEnv, env);
        wrapper.ne(AsrAccountDO::getStatus, AccountStatusEnum.INVALID.getStatus());
        List<AsrAccountDO> dbList = accountDao.getBaseMapper().selectList(wrapper);
        return !dbList.isEmpty() ? dbList.get(0) : null;
    }

    public List<AsrAccountDO> getAccountList(){
        LambdaQueryWrapper<AsrAccountDO> wrapper = Wrappers.<AsrAccountDO>lambdaQuery().eq(AsrAccountDO::getEnv, env);
        return accountDao.getBaseMapper().selectList(wrapper);
    }

    public void updatePassword(Long id, String oldPassword, String newPassword) {
        AsrAccountDO dbAccount = accountDao.getBaseMapper().selectById(id);
        if (dbAccount == null || Objects.equals(dbAccount.getStatus(), AccountStatusEnum.INVALID.getStatus())){
            throw new AsrCoreWarnException("用户不存在");
        }
        if (!Objects.equals(dbAccount.getPassword(), oldPassword)){
            throw new AsrCoreWarnException("旧密码不正确");
        }
        AsrAccountDO updateEntity = new AsrAccountDO();
        updateEntity.setId(id);
        updateEntity.setPassword(newPassword);
        updateEntity.setUpdateTime(new Date());
        updateEntity.setUpdateBy("用户修改密码");
        accountDao.getBaseMapper().updateById(updateEntity);
    }

    public void updateMyInfo(InspectorAccountDTO reqDto) {
        AsrAccountDO dbAccount = accountDao.getBaseMapper().selectById(reqDto.getId());
        if (dbAccount == null || Objects.equals(dbAccount.getStatus(), AccountStatusEnum.INVALID.getStatus())){
            throw new AsrCoreWarnException("用户不存在");
        }
        AsrAccountDO updateEntity = new AsrAccountDO();

        updateEntity.setId(reqDto.getId());
        updateEntity.setName(reqDto.getName());
        updateEntity.setMailAddress(reqDto.getMailAddress());
        updateEntity.setMailName(reqDto.getMailName());
        updateEntity.setMailPhone(reqDto.getMailPhone());
        updateEntity.setPaymentMethod(reqDto.getPaymentMethod());
        updateEntity.setPaymentCardNo(reqDto.getPaymentCardNo());
        updateEntity.setPaymentName(reqDto.getPaymentName());
        updateEntity.setUpdateBy("用户个人中心更新信息");
        updateEntity.setUpdateTime(new Date());

        accountDao.getBaseMapper().updateById(updateEntity);
    }
}
