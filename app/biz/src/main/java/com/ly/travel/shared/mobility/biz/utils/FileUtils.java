package com.ly.travel.shared.mobility.biz.utils;

import com.github.junrar.Archive;
import com.github.junrar.rarfile.FileHeader;
import com.ly.travel.shared.mobility.biz.model.vo.meta.FileMetaInfo;
import com.ly.travel.shared.mobility.biz.model.vo.meta.UploadFileInfoVO;
import lombok.extern.slf4j.Slf4j;
import net.sf.sevenzipjbinding.*;
import net.sf.sevenzipjbinding.impl.RandomAccessFileInStream;
import net.sf.sevenzipjbinding.simple.ISimpleInArchive;
import net.sf.sevenzipjbinding.simple.ISimpleInArchiveItem;
import org.apache.commons.compress.archivers.zip.ZipArchiveEntry;
import org.apache.commons.compress.archivers.zip.ZipFile;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.attribute.BasicFileAttributes;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * @Description: 文件处理
 * @Author: jay.he
 * @Date: 2025-08-18 15:49
 * @Version: 1.0
 **/
@Slf4j
public class FileUtils {

    /**
     * 解压文件并获取音视频文件的元数据信息
     *
     * @param zipFilePath 压缩文件路径
     * @return 音视频文件元数据列表
     */
    public static UploadFileInfoVO extractAndListMediaFiles(String filePath) {
        List<FileMetaInfo> fileList = new ArrayList<>();
//        // 创建解压目录
//        String extractDir = zipFilePath.substring(0, zipFilePath.lastIndexOf(".")) + "_extracted/";
//        new File(extractDir).mkdirs();
//
//        try {
//            if (zipFilePath.toLowerCase().endsWith(".zip")) {
//                // 解压ZIP文件
//                unzipMediaFiles(zipFilePath, extractDir);
//            } else if (zipFilePath.toLowerCase().endsWith(".rar")) {
//                // 解压RAR文件
//                unrarMediaFiles(zipFilePath, extractDir);
//            }
//
//            // 遍历解压后的目录，获取所有音视频文件（包括嵌套目录中的文件）
//            scanMediaFilesInDirectory(new File(extractDir), fileList);
//        } catch (Exception e) {
//            log.error("Error extracting and listing media files: {}", e);
//        }
        boolean needDel = true;
        try {
            if (filePath.toLowerCase().endsWith(".zip")) {
                // 创建解压目录
                String extractDir = filePath.substring(0, filePath.lastIndexOf(".")) + "_extracted/";
                new File(extractDir).mkdirs();
                // 解压ZIP文件
                unzipMediaFiles(filePath, extractDir);
                // 遍历解压后的目录，获取所有音视频文件（包括嵌套目录中的文件）
                scanMediaFilesInDirectory(new File(extractDir), fileList);
            } else if (filePath.toLowerCase().endsWith(".rar")) {
                // 创建解压目录
                String extractDir = filePath.substring(0, filePath.lastIndexOf(".")) + "_extracted/";
                new File(extractDir).mkdirs();
                // 解压RAR文件
                unrarMediaFiles(filePath, extractDir);
                // 遍历解压后的目录，获取所有音视频文件（包括嵌套目录中的文件）
                scanMediaFilesInDirectory(new File(extractDir), fileList);
            } else {
                // 处理单个音视频文件
                String extension = getFileExtension(filePath).toLowerCase();
                Set<String> mediaExtensions = new HashSet<>(Arrays.asList(
                        "mp3", "wav", "flac", "aac", "m4a", "wma", "ogg",  // 音频格式
                        "mp4", "avi", "mov", "wmv", "flv", "mkv", "webm"   // 视频格式
                ));

                if (mediaExtensions.contains(extension)) {
                    File file = new File(filePath);
                    if (file.exists() && file.isFile()) {
                        try {
                            FileMetaInfo metadata = getFileMetadata(file);
                            fileList.add(metadata);
                        } catch (IOException e) {
                            log.error("Error getting metadata for file: {}", filePath, e);
                        }
                    }
                } else {
                    log.warn("Unsupported file type: {}", filePath);
                }
                needDel = false;
            }
        } catch (Exception e) {
            log.error("Error extracting and listing media files: {}", e);
            e.printStackTrace();
        }

        List<FileMetaInfo> fileMetaInfoList = fileList.stream()
                .filter(fileMetaInfo -> !fileMetaInfo.getFileName().startsWith("."))
                .collect(Collectors.toList());

        return UploadFileInfoVO.builder()
                .fileMetaInfoList(fileMetaInfoList)
                .needDel(needDel)
                .build();
    }

    /**
     * 解压ZIP文件
     */
    public static void unzipMediaFiles(String zipFilePath, String extractDir) throws IOException {
        try (ZipFile zipFile = new ZipFile(new File(zipFilePath))) {
            Enumeration<ZipArchiveEntry> entries = zipFile.getEntries();

            while (entries.hasMoreElements()) {
                ZipArchiveEntry entry = entries.nextElement();
                String entryName = entry.getName();
                File destFile = new File(extractDir, entryName);

                if (entry.isDirectory()) {
                    destFile.mkdirs();
                } else {
                    // 确保父目录存在
                    destFile.getParentFile().mkdirs();

                    // 解压文件
                    try (InputStream is = zipFile.getInputStream(entry);
                         FileOutputStream fos = new FileOutputStream(destFile)) {
                        byte[] buffer = new byte[1024];
                        int length;
                        while ((length = is.read(buffer)) > 0) {
                            fos.write(buffer, 0, length);
                        }
                    }

                    // 设置文件的修改时间为压缩包中的时间
                    destFile.setLastModified(entry.getTime());
                }
            }
        }
    }

    /**
     * 解压RAR文件
     * 支持RAR v4及以下版本（使用junrar库）和RAR v5版本（使用7-Zip-JBinding库）
     */
    public static void unrarMediaFiles(String rarFilePath, String extractDir) throws Exception {
        try {
            unrarWithSevenZip(rarFilePath, extractDir);
            log.info("Successfully extracted RAR file using 7-Zip-JBinding as fallback: {}", rarFilePath);
        }catch (Exception e) {
            throw new RuntimeException("Failed to extract RAR file: " + rarFilePath +
                    ". 7-Zip-JBinding error: " + e.getMessage(), e);
        }
//        try {
//            // 首先尝试使用junrar库解压（适用于RAR v4及以下版本）
//            unrarWithJunrar(rarFilePath, extractDir);
//            log.info("Successfully extracted RAR file using junrar library: {}", rarFilePath);
//        } catch (UnsupportedRarV5Exception e) {
//            // 如果是RAR v5格式，使用7-Zip-JBinding库
//            log.warn("RAR v5 format detected, falling back to 7-Zip-JBinding: {}", rarFilePath);
//            unrarWithSevenZip(rarFilePath, extractDir);
//            log.info("Successfully extracted RAR v5 file using 7-Zip-JBinding: {}", rarFilePath);
//        } catch (Exception e) {
//            // 如果junrar失败且不是RAR v5异常，也尝试7-Zip-JBinding作为备选方案
//            log.warn("junrar extraction failed, trying 7-Zip-JBinding as fallback: {}", e.getMessage());
//            try {
//                unrarWithSevenZip(rarFilePath, extractDir);
//                log.info("Successfully extracted RAR file using 7-Zip-JBinding as fallback: {}", rarFilePath);
//            } catch (Exception fallbackException) {
//                log.error("Both junrar and 7-Zip-JBinding failed for file: {}", rarFilePath);
//                throw new Exception("Failed to extract RAR file: " + rarFilePath +
//                    ". junrar error: " + e.getMessage() +
//                    ", 7-Zip-JBinding error: " + fallbackException.getMessage(), e);
//            }
//        }
    }

    /**
     * 使用junrar库解压RAR文件（适用于RAR v4及以下版本）
     */
    private static void unrarWithJunrar(String rarFilePath, String extractDir) throws Exception {
        try (Archive archive = new Archive(new File(rarFilePath))) {
            FileHeader fileHeader;
            while ((fileHeader = archive.nextFileHeader()) != null) {
                String fileName = fileHeader.getFileNameString().trim();
                File destFile = new File(extractDir, fileName);

                if (fileHeader.isDirectory()) {
                    destFile.mkdirs();
                } else {
                    // 确保父目录存在
                    destFile.getParentFile().mkdirs();

                    // 解压文件
                    try (FileOutputStream fos = new FileOutputStream(destFile)) {
                        archive.extractFile(fileHeader, fos);
                    }

                    // 设置文件的修改时间
                    if (fileHeader.getMTime() != null) {
                        destFile.setLastModified(fileHeader.getMTime().getTime());
                    }
                }
            }
        }
    }

    /**
     * 使用7-Zip-JBinding库解压RAR文件（适用于RAR v5版本）
     */
    private static void unrarWithSevenZip(String rarFilePath, String extractDir) throws Exception {
        RandomAccessFile randomAccessFile = null;
        IInArchive inArchive = null;

        try {
            // 确保解压目录存在
            File extractDirFile = new File(extractDir);
            if (!extractDirFile.exists()) {
                extractDirFile.mkdirs();
            }

            // 打开RAR文件
            randomAccessFile = new RandomAccessFile(rarFilePath, "r");
            RandomAccessFileInStream randomAccessFileStream = new RandomAccessFileInStream(randomAccessFile);

            // 创建归档对象
            inArchive = SevenZip.openInArchive(null, randomAccessFileStream);
            ISimpleInArchive simpleInArchive = inArchive.getSimpleInterface();

            // 遍历归档中的所有项目
            for (ISimpleInArchiveItem item : simpleInArchive.getArchiveItems()) {
                if (item.isFolder()) {
                    // 创建目录
                    String folderPath = item.getPath();
                    if (folderPath != null && !folderPath.trim().isEmpty()) {
                        File folder = new File(extractDir, folderPath);
                        folder.mkdirs();
                    }
                } else {
                    // 解压文件
                    String filePath = item.getPath();
                    if (filePath != null && !filePath.trim().isEmpty()) {
                        File destFile = new File(extractDir, filePath);

                        // 确保父目录存在
                        File parentDir = destFile.getParentFile();
                        if (parentDir != null && !parentDir.exists()) {
                            parentDir.mkdirs();
                        }

                        // 提取文件内容
                        ExtractOperationResult result;
                        try (FileOutputStream fos = new FileOutputStream(destFile)) {
                            result = item.extractSlow(data -> {
                                try {
                                    fos.write(data);
                                } catch (IOException e) {
                                    throw new RuntimeException(e);
                                }
                                return data.length;
                            });
                        }

                        if (result != ExtractOperationResult.OK) {
                            throw new Exception("Failed to extract file: " + filePath + ", result: " + result);
                        }

                        // 设置文件的修改时间
                        Date modificationTime = item.getLastWriteTime();
                        if (modificationTime != null) {
                            destFile.setLastModified(modificationTime.getTime());
                        }

                        log.debug("Extracted file: {}", filePath);
                    }
                }
            }
        } finally {
            // 清理资源
            if (inArchive != null) {
                try {
                    inArchive.close();
                } catch (Exception e) {
                    log.warn("Failed to close archive: {}", e.getMessage());
                }
            }
            if (randomAccessFile != null) {
                try {
                    randomAccessFile.close();
                } catch (Exception e) {
                    log.warn("Failed to close random access file: {}", e.getMessage());
                }
            }
        }
    }

    /**
     * 扫描目录中的媒体文件并获取元信息
     */
    public static void scanMediaFilesInDirectory(File directory, List<FileMetaInfo> fileList) {
        // 定义音视频文件扩展名
        Set<String> mediaExtensions = new HashSet<>(Arrays.asList(
                "mp3", "wav", "flac", "aac", "m4a", "wma", "ogg",  // 音频格式
                "mp4", "avi", "mov", "wmv", "flv", "mkv", "webm"   // 视频格式
        ));

        scanDirectory(directory, mediaExtensions, fileList);
    }

    /**
     * 递归扫描目录
     */
    public static void scanDirectory(File directory, Set<String> mediaExtensions, List<FileMetaInfo> fileList) {
        File[] files = directory.listFiles();
        if (files == null) return;

        for (File file : files) {
            if (file.isDirectory()) {
                // 递归扫描子目录
                scanDirectory(file, mediaExtensions, fileList);
            } else {
                String fileName = file.getName();
                String extension = getFileExtension(fileName).toLowerCase();

                if (mediaExtensions.contains(extension)) {
                    try {
                        FileMetaInfo metadata = getFileMetadata(file);
                        fileList.add(metadata);
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
            }
        }
    }

    /**
     * 获取文件元信息
     */
    public static FileMetaInfo getFileMetadata(File file) throws IOException {
        Path path = file.toPath();
        BasicFileAttributes attrs = Files.readAttributes(path, BasicFileAttributes.class);

        return FileMetaInfo.builder()
                .fileName(file.getName())
                .filePath(file.getAbsolutePath())
                .fileSize(attrs.size())
                .fileSizeStr(formatFileSize(attrs.size()))
                .createTime(new Date(attrs.creationTime().toMillis()))
                .modifyTime(new Date(attrs.lastModifiedTime().toMillis()))
                .extension(getFileExtension(file.getName()))
                .isReadable(file.canRead())
                .build();
    }

    /**
     * 格式化文件大小
     */
    public static String formatFileSize(long size) {
        if (size <= 0) return "0 B";

        final String[] units = new String[]{"B", "KB", "MB", "GB", "TB"};
        int digitGroups = (int) (Math.log10(size) / Math.log10(1024));

        return String.format("%.2f %s", size / Math.pow(1024, digitGroups), units[digitGroups]);
    }

    /**
     * 获取文件扩展名
     */
    public static String getFileExtension(String fileName) {
        int lastIndexOf = fileName.lastIndexOf(".");
        if (lastIndexOf == -1) {
            return "";
        }
        return fileName.substring(lastIndexOf + 1);
    }

    /**
     * 异步删除文件
     *
     * @param filePath 文件路径
     * @return CompletableFuture
     */
    public static CompletableFuture<Boolean> delFileAsync(String filePath) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                delFile(filePath);
                return true;
            } catch (Exception e) {
                log.error("异步删除文件失败: {}", filePath, e);
                return false;
            }
        });
    }

    /**
     * 删除文件
     *
     * @param filePath 文件路径
     * @return
     */
    public static boolean delFile(String filePath) {
        if (filePath == null || filePath.trim().isEmpty()) {
            throw new IllegalArgumentException("文件路径不能为空");
        }

        try {
            Path path = Paths.get(filePath);
            Files.deleteIfExists(path); // 如果文件存在则删除
            log.info("文件删除成功: {}", filePath);
            return true;
        } catch (IOException e) {
            log.error("删除文件失败: {}", filePath, e);
            throw new RuntimeException("删除文件失败", e);
        }
    }
}
