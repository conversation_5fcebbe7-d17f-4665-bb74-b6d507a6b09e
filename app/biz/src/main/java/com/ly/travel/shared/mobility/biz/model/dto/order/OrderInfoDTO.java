package com.ly.travel.shared.mobility.biz.model.dto.order;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description:
 * @Author: jay.he
 * @Date: 2025-08-19 11:36
 * @Version: 1.0
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderInfoDTO {

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 上车时间
     */
    private String passengerOnCarTime;

    /**
     * 下车时间
     */
    private String passengerArriveTime;

    /**
     * 车牌号
     */
    private String carNo;

    /**
     * 司机姓名
     */
    private String driverName;

    /**
     * 订单类型
     */
    private Integer orderType;
}
