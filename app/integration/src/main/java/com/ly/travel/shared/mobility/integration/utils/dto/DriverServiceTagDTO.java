package com.ly.travel.shared.mobility.integration.utils.dto;

import com.google.common.collect.Lists;
import com.ly.sof.utils.mapping.JacksonUtils;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Arrays;
import java.util.List;

/**
 * @Description: 司机服务标签
 * @Author: jay.he
 * @Date: 2025-08-26 13:30
 * @Version: 1.0
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DriverServiceTagDTO {

    private List<TagInfo> serviceTagList;

    public static DriverServiceTagDTO getDefault() {
        return new DriverServiceTagDTO(Lists.newArrayList());
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class TagInfo {

        private List<String> supportOrderTypes;

        private String tagName;

        private String tagCode;
    }

    public static void main(String[] args) {

        List<String> orderTypes = Arrays.asList("11", "12", "13", "14", "15", "19", "80");
        DriverServiceTagDTO.TagInfo tagInfo1 = DriverServiceTagDTO.TagInfo.builder()
                .supportOrderTypes(orderTypes)
                .tagName("礼貌用语")
                .tagCode("礼貌用语")
                .build();
        // 依次生成其它司机服务标签：提前联系、提供瓶装水、不闲聊、开关车门、提拿行李、着装统一、接送进小区
        DriverServiceTagDTO.TagInfo tagInfo2 = DriverServiceTagDTO.TagInfo.builder()
                .supportOrderTypes(orderTypes)
                .tagName("提前联系")
                .tagCode("提前联系")
                .build();
        DriverServiceTagDTO.TagInfo tagInfo3 = DriverServiceTagDTO.TagInfo.builder()
                .supportOrderTypes(orderTypes)
                .tagName("提供瓶装水")
                .tagCode("提供瓶装水")
                .build();
        DriverServiceTagDTO.TagInfo tagInfo4 = DriverServiceTagDTO.TagInfo.builder()
                .supportOrderTypes(orderTypes)
                .tagName("不闲聊")
                .tagCode("不闲聊")
                .build();
        DriverServiceTagDTO.TagInfo tagInfo5 = DriverServiceTagDTO.TagInfo.builder()
                .supportOrderTypes(orderTypes)
                .tagName("开关车门")
                .tagCode("开关车门")
                .build();
        DriverServiceTagDTO.TagInfo tagInfo6 = DriverServiceTagDTO.TagInfo.builder()
                .supportOrderTypes(orderTypes)
                .tagName("提拿行李")
                .tagCode("提拿行李")
                .build();
        DriverServiceTagDTO.TagInfo tagInfo7 = DriverServiceTagDTO.TagInfo.builder()
                .supportOrderTypes(orderTypes)
                .tagName("着装统一")
                .tagCode("着装统一")
                .build();
        DriverServiceTagDTO.TagInfo tagInfo8 = DriverServiceTagDTO.TagInfo.builder()
                .supportOrderTypes(orderTypes)
                .tagName("接送进小区")
                .tagCode("接送进小区")
                .build();

        List<TagInfo> list = Lists.newArrayList();
        list.add(tagInfo1);
        list.add(tagInfo2);
        list.add(tagInfo3);
        list.add(tagInfo4);
        list.add(tagInfo5);
        list.add(tagInfo6);
        list.add(tagInfo7);
        list.add(tagInfo8);
        System.out.println(JacksonUtils.toJSONString(DriverServiceTagDTO.builder().serviceTagList(list).build()));

    }
}
