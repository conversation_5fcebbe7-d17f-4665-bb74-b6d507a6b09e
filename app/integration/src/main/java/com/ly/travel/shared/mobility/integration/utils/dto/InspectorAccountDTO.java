package com.ly.travel.shared.mobility.integration.utils.dto;

import lombok.Data;


@Data
public class InspectorAccountDTO {

    private Long id;
    /**
     * 用户账号
     */
    private String userAccount;
    /**
     * 用户名称
     */
    private String name;

    private String password;

    private String mobileNo;
    /**
     * 邮寄地址
     */
    private String mailAddress;

    private Integer status;

    private String updateBy;

    private String updateTime;

    /**
     * 收款方式,1:支付宝,2:银行卡
     */
    private Integer paymentMethod;

    /**
     * 收款卡号
     */
    private String paymentCardNo;

    /**
     * 收款人姓名
     */
    private String paymentName;

    /**
     * 收件人姓名
     */
    private String mailName;

    /**
     * 收件人手机号
     */
    private String mailPhone;
}
