<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:p="http://www.springframework.org/schema/p"
	xmlns:sof="http://schema.ly.com/schema/sof"
	xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
	xmlns:context="http://www.springframework.org/schema/context"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
         http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd
         http://schema.ly.com/schema/sof http://schema.ly.com/schema/sof.xsd
         http://code.alibabatech.com/schema/dubbo http://code.alibabatech.com/schema/dubbo/dubbo.xsd"
	default-autowire="byName">

	<!--order.service-->
	<sof:reference id="ivrCarOrderFacade"
				   serviceName="ivr"
				   gsName="${dsf.car.shared.mobility.order.service.gsName}"
				   version="${dsf.car.shared.mobility.order.service.version}"
				   timeout="5000"
				   retries="0"
				   serializer="JACKSON"
				   interface="com.ly.travel.car.orderservice.facade.IVRCarOrderFacade">
		<sof:method name="orderList"  paramType="bodyParam" timeout="10000" retries="0"/>
		<sof:method name="orderDetail"  paramType="bodyParam" timeout="5000" retries="0"/>
	</sof:reference>

	<!--order.core-->
	<sof:reference id="carOrderFacade"
				   serviceName="order"
				   gsName="${dsf.car.shared.mobility.order.core.gsName}"
				   version="${dsf.car.shared.mobility.order.core.version}"
				   timeout="5000"
				   retries="0"
				   serializer="JACKSON"
				   interface="com.ly.travel.car.ordercore.facade.OrderFacade">
		<sof:method name="simpleDetailList"  paramType="bodyParam" timeout="10000" retries="0"/>
	</sof:reference>

</beans>
