<template>
    <div class="settings-container">
        <!-- 左侧导航 -->
        <div class="sidebar">
            <div class="nav-item active">基本设置</div>
        </div>

        <!-- 右侧表单 -->
        <div class="main-content">
            <el-form
                    label-width="120px"
                    :model="formData"
                    size="small"
                    ref="formData"
                    class="user-form"
            >
                <!-- 基础信息部分 -->
                <div class="form-section">
                    <div class="section-title">
                        <i class="el-icon-user"></i>
                        基础信息
                    </div>

                    <el-form-item label="手机号" prop="mobileNo">
                        <el-input v-model="formData.mobileNo" disabled class="basic-input"></el-input>
                    </el-form-item>

                    <el-form-item label="密码">
                        <div class="password-container">
                            <el-input v-model="password" class="basic-input"></el-input>
                            <el-button type="text" @click="handleModifyPassword" class="modify-password-btn">修改密码</el-button>
                        </div>
                    </el-form-item>

                    <el-form-item label="用户姓名" prop="name" :rules="[{required: true, message: '用户姓名不能为空' }]">
                        <el-input v-model="formData.name" class="basic-input"></el-input>
                    </el-form-item>

                    <el-form-item label="收款方式" prop="paymentMethod">
                        <el-select v-model="formData.paymentMethod" placeholder="请选择收款方式" class="basic-input">
                            <el-option label="支付宝" :value="1"></el-option>
                            <el-option label="银行卡" :value="2"></el-option>
                        </el-select>
                    </el-form-item>

                    <el-form-item label="收款人姓名" prop="paymentName" v-if="formData.paymentMethod === 2">
                        <el-input v-model="formData.paymentName" class="basic-input"></el-input>
                    </el-form-item>

                    <el-form-item label="卡号" prop="paymentCardNo">
                        <el-input v-model="formData.paymentCardNo" class="basic-input"></el-input>
                    </el-form-item>
                </div>

                <!-- 分割线 -->
                <div class="section-divider"></div>

                <!-- 地址信息部分 -->
                <div class="form-section">
                    <div class="section-title">
                        <i class="el-icon-location"></i>
                        地址信息
                    </div>

                    <el-form-item label="收件人姓名" prop="mailName">
                        <el-input v-model="formData.mailName" class="address-input"></el-input>
                    </el-form-item>

                    <el-form-item label="收件人手机号" prop="mailPhone">
                        <el-input v-model="formData.mailPhone" class="address-input"></el-input>
                    </el-form-item>

                    <el-form-item label="邮寄地址" prop="mailAddress">
                        <el-input v-model="formData.mailAddress" maxlength="64" class="address-input"></el-input>
                    </el-form-item>
                </div>

                <!-- 提交按钮 -->
                <el-form-item class="submit-section">
                    <el-button type="primary" @click="handleSubmit" class="submit-btn">更新信息</el-button>
                </el-form-item>
            </el-form>
        </div>

        <!-- 修改密码弹窗 -->
        <el-dialog
                title="修改密码"
                :visible.sync="modifyPasswordDialogVisible"
                width="600px"
                @close="resetPasswordForm"
                :close-on-click-modal="false"
        >
            <el-form
                    :model="passwordForm"
                    :rules="passwordRules"
                    ref="passwordForm"
                    label-width="140px"
            >
                <el-form-item label="原密码" prop="oldPassword">
                    <el-input v-model="passwordForm.oldPassword" type="password"></el-input>
                </el-form-item>
                <el-form-item label="新密码" prop="newPassword">
                    <el-input v-model="passwordForm.newPassword" type="password"></el-input>
                </el-form-item>
                <el-form-item label="再次输入新密码" prop="confirmNewPassword">
                    <el-input v-model="passwordForm.confirmNewPassword" type="password"></el-input>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="modifyPasswordDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="submitPasswordChange">提交</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
export default {
    data() {
        // 验证规则
        const validatePassword = (rule, value, callback) => {
            if (!value) {
                callback(new Error('请输入密码'));
            } else if (value.length < 6) {
                callback(new Error('密码长度不能少于6位'));
            } else {
                callback();
            }
        };

        const validateConfirmPassword = (rule, value, callback) => {
            if (value !== this.passwordForm.newPassword) {
                callback(new Error('两次输入的密码不一致'));
            } else {
                callback();
            }
        };

        return {
            formData: {
                mobileNo: '',
                password: '',
                mobile: '',
                mailName: '',
                mailPhone: '',
                mailAddress: '',
                paymentMethod: 2,
                paymentName: '',
                paymentCardNo: ''
            },
            password:'*********',
            modifyPasswordDialogVisible: false,
            passwordForm: {
                oldPassword: '',
                newPassword: '',
                confirmNewPassword: ''
            },
            passwordRules: {
                oldPassword: [{ validator: validatePassword, trigger: 'blur' }],
                newPassword: [{ validator: validatePassword, trigger: 'blur' }],
                confirmNewPassword: [{ validator: validateConfirmPassword, trigger: 'blur' }]
            }
        }
    },
    created() {
        this.getUserInfo()
    },
    methods: {
        async getUserInfo() {
            this.loading = true
            try {
                const param = {
                    url: 'inspector/getUserInfo',
                    method: 'POST'
                }
                const res = await this.$http(param)
                if (res.success) {
                    this.formData = res.data || {}
                } else {
                    this.$message.error(res.msg || '获取用户信息失败')
                }
            } catch (error) {
                console.error('获取用户信息失败:', error)
                this.$message.error('获取用户信息失败')
            } finally {
                this.loading = false
            }
        },
        handleModifyPassword() {
            this.modifyPasswordDialogVisible = true;
        },

        resetPasswordForm() {
            this.$refs.passwordForm.resetFields();
        },

        submitPasswordChange() {
            this.$refs.passwordForm.validate(valid => {
                if (valid) {
                    // 提交修改密码请求
                    this.$http({
                        url: 'myInfo/updatePassword',
                        method: 'POST',
                        data: {
                            id: this.formData.id,
                            oldPassword: this.passwordForm.oldPassword,
                            newPassword: this.passwordForm.newPassword
                        }
                    }).then(res => {
                        if (res.success) {
                            this.$message.success('密码修改成功');
                            this.modifyPasswordDialogVisible = false;
                            this.resetPasswordForm();
                        } else {
                            this.$message.error(res.msg || '密码修改失败');
                        }
                    }).catch(err => {
                        this.$message.error('网络错误，请重试');
                    });
                } else {

                }
            });
        },

        handleSubmit() {
            this.$refs.formData.validate(valid => {
                if (valid) {
                    // 提交修改密码请求
                    this.$http({
                        url: 'myInfo/updateMyInfo',
                        method: 'POST',
                        data: this.formData
                    }).then(res => {
                        if (res.success) {
                            this.$message.success('信息更新成功');
                            this.getUserInfo();
                        } else {
                            this.$message.error(res.msg || '信息更新失败');
                        }
                    }).catch(err => {
                        this.$message.error('网络错误，请重试');
                    });
                } else {

                }
            })
        }
    }
}
</script>

<style scoped>
.settings-container {
    display: flex;
    height: 100vh;
    background-color: #f5f7fa;
}

.sidebar {
    width: 200px;
    background-color: #fff;
    border-right: 1px solid #e6e6e6;
    padding: 20px 0;
}

.nav-item {
    padding: 12px 20px;
    cursor: pointer;
    font-size: 14px;
    color: #666;
    border-bottom: 1px solid #eee;
}

.nav-item.active {
    background-color: #e6f7ff;
    color: #007aff;
    border-left: 3px solid #007aff;
}

.main-content {
    flex: 1;
    padding: 30px;
    background-color: #fff;
    overflow-y: auto;
    max-height: 100vh;
}

.user-form {
    max-width: 700px;
}

.el-form-item__label {
    font-weight: 500;
    color: #333;
}

/* 表单分组样式 */
.form-section {
    margin-bottom: 20px;
    padding: 15px;
    background: #fafafa;
    border-radius: 6px;
    border: 1px solid #e8e8e8;
}

.section-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #007aff;
    display: flex;
    align-items: center;
}

.section-title i {
    margin-right: 8px;
    color: #007aff;
    font-size: 18px;
}

/* 分割线样式 */
.section-divider {
    height: 1px;
    background: #e8e8e8;
    margin: 20px 0;
}

/* 基础信息输入框样式 - 以收款方式选择框宽度为基准 */
.basic-input {
    width: 280px;
}

/* 地址信息输入框样式 - 比基础信息宽50% */
.address-input {
    width: 420px;
}

/* 密码容器样式 */
.password-container {
    display: flex;
    align-items: center;
    gap: 10px;
}

.modify-password-btn {
    color: #007aff;
    font-size: 14px;
    padding: 0;
    margin: 0;
    white-space: nowrap;
}

.modify-password-btn:hover {
    color: #005bb5;
}

/* 提交按钮区域 */
.submit-section {
    margin-top: 30px;
    text-align: center;
    padding-top: 15px;
    border-top: 1px solid #e8e8e8;
}

.submit-btn {
    background-color: #007aff;
    border-color: #007aff;
    color: white;
    padding: 12px 30px;
    font-size: 14px;
    border-radius: 6px;
    min-width: 120px;
}

.submit-btn:hover {
    background-color: #005bb5;
    border-color: #005bb5;
}

/* 表单项间距调整 */
.form-section .el-form-item {
    margin-bottom: 15px;
}

.form-section .el-form-item:last-child {
    margin-bottom: 0;
}
</style>
