<template>
  <div class="layout-with-menu">
    <!-- 顶部标题栏 -->
    <Header 
      :show-menu="true" 
      :show-user-info="true"
      :is-collapse="isCollapse"
      @toggle-sidebar="toggleSidebar"
    />

    <!-- 主体内容区域 -->
    <div class="main-container">
      <!-- 左侧菜单 -->
      <Sidebar :is-collapse="isCollapse" />

      <!-- 右侧内容区域 -->
      <div class="content-wrapper">
        <div class="content">
          <router-view :key="$route.fullPath" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Header from './components/Header'
import Sidebar from './components/Sidebar'

export default {
  name: 'LayoutWithMenu',
  components: {
    Header,
    Sidebar
  },
  data() {
    return {
      isCollapse: false
    }
  },
  methods: {
    toggleSidebar() {
      this.isCollapse = !this.isCollapse
    }
  }
}
</script>

<style lang="scss" scoped>
.layout-with-menu {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-container {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.content-wrapper {
  flex: 1;
  background-color: #f5f5f5;
  overflow: auto;

  .content {
    padding: 20px;
    min-height: calc(100vh - 100px);
    background-color: white;
    margin: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}
</style>
