<template>
    <div id="app">
        <Header
            :show-menu="false"
            :show-user-info="showUserInfo"
        />
        <div class="main-content">
            <AppMain/>
        </div>
    </div>

</template>

<script>
import AppMain from "./components/AppMain";
import Header from "./components/Header";
import { setSession } from '@/utils/util'


export default {
    name: "Layout",
    components: {
        AppMain,
        Header,
    },
    computed: {
        showUserInfo() {
            // 登录页面和完善信息页面不显示用户信息
            const hideUserInfoPages = ['/login', '/improveInfo']
            return !hideUserInfoPages.includes(this.$route.path)
        }
    },
    data() {
        return {
            collapse: false,
            /**
             * tips菜单页面 当前使用/config/config-tab.json
             * 后续接入权限后可以从同一权限接口中载入
             */
            menuData: [],
            /**
             * 允许多开的页面
             * menuData>>item>>code
             */
            multTabs: ["home-2"]
        }
    },
    mounted() {
    },
    methods: {
    },
    created() {
    }
};
</script>

<style lang="scss" scoped>
#app {
    height: 100vh;
    display: flex;
    flex-direction: column;
}

.main-content {
    flex: 1;
    overflow: auto;
}
</style>
