<template>
  <div class="inspector-layout">
    <!-- 顶部标题栏 -->
    <div class="header">
      <div class="header-content">
        <div class="header-left">
          <div class="hamburger-container" @click="toggleSidebar">
            <svg
              :class="{'is-active': !isCollapse}"
              class="hamburger"
              viewBox="0 0 1024 1024"
              xmlns="http://www.w3.org/2000/svg"
              width="64"
              height="64"
            >
              <path d="M408 442h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zm-8 204c0 4.4 3.6 8 8 8h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56zm504-486H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8z"/>
            </svg>
          </div>
          <h1 class="system-title">质量抽检系统</h1>
        </div>
        <div class="user-info">
          <span class="welcome-text">欢迎，{{ userInfo.name || userInfo.userAccount }}</span>
          <el-button type="text" @click="logout" class="logout-btn">退出</el-button>
        </div>
      </div>
    </div>

    <!-- 主体内容区域 -->
    <div class="main-container">
      <!-- 左侧菜单 -->
      <div class="sidebar" :class="{'sidebar-collapse': isCollapse}">
        <el-menu
          :default-active="activeMenu"
          :collapse="isCollapse"
          class="sidebar-menu"
          background-color="#304156"
          text-color="#bfcbd9"
          active-text-color="#409EFF"
          router
        >
          <el-menu-item index="/inspector/profile" class="menu-item">
            <i class="el-icon-user"></i>
            <span slot="title">个人中心</span>
          </el-menu-item>
          <el-menu-item index="/asrOrderList" class="menu-item">
            <i class="el-icon-document"></i>
            <span slot="title">订单列表</span>
          </el-menu-item>
        </el-menu>
      </div>

      <!-- 右侧内容区域 -->
      <div class="content-wrapper">
        <div class="content">
          <router-view :key="$route.fullPath" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'InspectorLayout',
  data() {
    return {
      userInfo: {},
      isCollapse: false
    }
  },
  computed: {
    activeMenu() {
      return this.$route.path
    }
  },
  created() {
    this.getUserInfo()
  },
  methods: {
    toggleSidebar() {
      this.isCollapse = !this.isCollapse
    },
    async getUserInfo() {
      try {
        const param = {
          url: 'inspector/getUserInfo',
          method: 'POST'
        }
        const res = await this.$http(param)
        if (res.success) {
          this.userInfo = res.data || {}
        }
      } catch (error) {
        console.error('获取用户信息失败:', error)
      }
    },
    logout() {
      this.$confirm('确认退出登录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 清除本地存储的登录信息
        localStorage.removeItem('inspector_token')
        localStorage.removeItem('inspector_account')
        // 跳转到登录页
        this.$router.push('/login')
        this.$message.success('已退出登录')
      }).catch(() => {
        // 取消退出
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.inspector-layout {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.header {
  height: 60px;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 1000;

  .header-content {
    height: 100%;
    padding: 0 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .header-left {
      display: flex;
      align-items: center;
      gap: 15px;

      .hamburger-container {
        cursor: pointer;
        padding: 8px;
        border-radius: 4px;
        transition: background-color 0.3s;

        &:hover {
          background-color: rgba(255, 255, 255, 0.1);
        }

        .hamburger {
          width: 20px;
          height: 20px;
          fill: white;
          transition: transform 0.3s;

          &.is-active {
            transform: rotate(90deg);
          }
        }
      }

      .system-title {
        color: white;
        font-size: 24px;
        font-weight: 600;
        margin: 0;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
      }
    }

    .user-info {
      display: flex;
      align-items: center;
      gap: 15px;

      .welcome-text {
        color: white;
        font-size: 14px;
      }

      .logout-btn {
        color: white;
        font-size: 14px;

        &:hover {
          color: #f0f0f0;
        }
      }
    }
  }
}

.main-container {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.sidebar {
  width: 200px;
  background-color: #304156;
  box-shadow: 2px 0 6px rgba(0, 0, 0, 0.1);
  transition: width 0.3s;

  &.sidebar-collapse {
    width: 64px;
  }

  .sidebar-menu {
    border: none;
    height: 100%;

    .menu-item {
      height: 50px;
      line-height: 50px;

      i {
        margin-right: 8px;
        font-size: 16px;
      }
    }
  }
}

.content-wrapper {
  flex: 1;
  background-color: #f5f5f5;
  overflow: auto;

  .content {
    padding: 20px;
    min-height: calc(100vh - 100px);
    background-color: white;
    margin: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .sidebar {
    width: 60px;

    .sidebar-menu {
      .menu-item {
        span {
          display: none;
        }
      }
    }
  }

  .header {
    .header-content {
      .system-title {
        font-size: 18px;
      }
    }
  }
}
</style>
