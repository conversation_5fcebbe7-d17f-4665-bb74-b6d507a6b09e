<template>
  <div class="sidebar" :class="{'sidebar-collapse': isCollapse}">
    <el-menu
      :default-active="activeMenu"
      :collapse="isCollapse"
      class="sidebar-menu"
      background-color="#304156"
      text-color="#bfcbd9"
      active-text-color="#409EFF"
      router
    >
      <el-menu-item index="/inspector/profile" class="menu-item">
        <i class="el-icon-user"></i>
        <span slot="title">个人中心</span>
      </el-menu-item>
      <el-menu-item index="/asrOrderList" class="menu-item">
        <i class="el-icon-document"></i>
        <span slot="title">订单列表</span>
      </el-menu-item>
    </el-menu>
  </div>
</template>

<script>
export default {
  name: 'Sidebar',
  props: {
    isCollapse: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    activeMenu() {
      return this.$route.path
    }
  }
}
</script>

<style lang="scss" scoped>
.sidebar {
  width: 200px;
  background-color: #304156;
  box-shadow: 2px 0 6px rgba(0, 0, 0, 0.1);
  transition: width 0.3s;

  &.sidebar-collapse {
    width: 64px;
  }

  .sidebar-menu {
    border: none;
    height: 100%;

    .menu-item {
      height: 50px;
      line-height: 50px;

      i {
        margin-right: 8px;
        font-size: 16px;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .sidebar {
    width: 60px;

    .sidebar-menu {
      .menu-item {
        span {
          display: none;
        }
      }
    }
  }
}
</style>
