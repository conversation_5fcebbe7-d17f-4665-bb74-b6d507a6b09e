package com.ly.travel.shared.mobility.dal.mybatisplus.travelcarasr.mapper;

import com.ly.travel.shared.mobility.dal.mybatisplus.travelcarasr.model.AsrRecognitionRecordDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 语音识别结果记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025/08/25 17:52
 */
@Mapper
public interface AsrRecognitionRecordMapper extends BaseMapper<AsrRecognitionRecordDO> {

    /**
     * 清理taskId下的识别结果 -- 置为失效
     *
     * @param taskId
     * @param env
     */
    void invalidRecords(@Param("taskId") String taskId, @Param("env") String env);

    /**
     * 根据taskId查询有效的识别结果
     *
     * @param taskId
     * @param env
     * @return
     */
    List<AsrRecognitionRecordDO> listValidRecords(@Param("taskId") String taskId, @Param("env") String env);

}
