package com.ly.travel.shared.mobility.web.controller;

import com.ly.travel.shared.mobility.biz.model.resp.AsrCoreBaseResp;
import com.ly.travel.shared.mobility.biz.service.AccountService;
import com.ly.travel.shared.mobility.biz.service.InspectorService;
import com.ly.travel.shared.mobility.integration.utils.dto.InspectorAccountDTO;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Map;

/**
 * <AUTHOR> By Houce
 * @since 2025/8/25
 */
@RestController
@RequestMapping("myInfo")
public class MyInfoController {

    @Resource
    private InspectorService inspectorService;
    @Resource
    private AccountService accountService;

    @RequestMapping("queryMyInfo")
    public InspectorAccountDTO queryMyInfo(){
        return inspectorService.getUserInfo(false);
    }

    @PostMapping("updatePassword")
    public AsrCoreBaseResp<?> updatePassword(@RequestBody Map<String,Object> reqMap){
        String oldPassword = reqMap.get("oldPassword").toString();
        String newPassword = reqMap.get("newPassword").toString();
        Long id = Long.parseLong(reqMap.get("id").toString());
        accountService.updatePassword(id,oldPassword,newPassword);
        return AsrCoreBaseResp.ok();
    }

    @PostMapping("updateMyInfo")
    public AsrCoreBaseResp<?> updateMyInfo(@RequestBody InspectorAccountDTO reqDto){
        accountService.updateMyInfo(reqDto);
        return AsrCoreBaseResp.ok();
    }
}
